.clamp-text {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

ion-segment {
  flex: 1;
  --background: transparent;
  min-width: 0;
  margin-right: 8px;
}

ion-segment-button {
  --color: var(--ion-color-medium);
  --indicator-color: var(--ion-color-primary);
  min-width: auto;
  font-size: 0.9rem;
}
ion-segment[mode="ios"] ion-segment-button {
  --color-checked: var(--ion-color-light);
}

/* Base styles */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --swiper-wrapper-transition-timing-function: linear !important;
}

/* schedule-x calendar */
button[class*="sx__month-agenda-day"] {
  color: var(--sx-internal-color-text) !important; /* override buttontext browser color */
}
.sx-vue-calendar-wrapper {
  max-width: 100vw;
  height: 800px;
  max-height: 90vh;
}
.sx__month-grid-day__events {
  cursor: pointer;
}

/* Container styles */
.page-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  width: 100%;
  background: var(--ion-background-color);
  border-radius: 0;
}

@media (min-width: 1400px) {
  .page-container {
    border-radius: 24px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  }
}

/* Link styles */
a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

.card {
  padding: 2em;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
}
</style>