<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/branches" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>我的收藏分會</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <div v-if="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>載入中...</p>
      </div>

      <div v-else-if="likedBranches.length === 0" class="empty-container">
        <ion-icon :icon="heartOutline" class="empty-icon"></ion-icon>
        <h3>您還沒有收藏任何分會</h3>
        <p>瀏覽分會並點擊愛心圖標來收藏</p>
        <ion-button router-link="/branches" fill="outline" class="browse-button">
          瀏覽分會
        </ion-button>
      </div>

      <div v-else class="page-container">
        <div class="branches-container ion-padding">
          <!-- Liked Branches -->
          <div class="all-branches">
            <div class="branches-grid">
              <ion-card
                v-for="branch in likedBranches"
                :key="branch.id"
                class="branch-card"
                button
                :router-link="`/branches/${branch.id}`"
              >
                <div class="banner-container">
                  <img :src="branch.banner || 'https://images.unsplash.com/photo-1517048676732-d65bc937f952'" :alt="branch.name" class="branch-banner" />
                  <div class="branch-actions">
                    <ion-button fill="clear" @click.prevent.stop="toggleFavorite(branch)" class="action-button">
                      <ion-icon :icon="heart" />
                    </ion-button>
                  </div>
                </div>
                <ion-card-header>
                  <div class="branch-info">
                    <img :src="branch.logo || 'https://images.unsplash.com/photo-1522071820081-009f0129c71c'" :alt="branch.name" class="branch-logo" />
                    <div class="branch-details">
                      <ion-card-title>{{ branch.name }}</ion-card-title>
                      <div class="branch-meta">
                        <span class="category-tag">{{ getCategoryTitle(branch.category_id) }}</span>
                        <span class="district-tag">{{ branch.district }}</span>
                      </div>
                    </div>
                  </div>
                  <ion-card-subtitle v-if="branch.description">{{ branch.description }}</ion-card-subtitle>
                </ion-card-header>
                <ion-card-content>
                  <div class="branch-stats">
                    <div class="stat-item">
                      <ion-icon :icon="peopleOutline"></ion-icon>
                      <span>{{ branch.member_count || 0 }} 位成員</span>
                    </div>
                    <div class="stat-item">
                      <ion-icon :icon="timeOutline"></ion-icon>
                      <span>{{ formatDate(branch.created_at) }}</span>
                    </div>
                  </div>
                </ion-card-content>
              </ion-card>
            </div>
          </div>
        </div>
      </div>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonCardContent,
  IonIcon,
  IonButton,
  IonSpinner,
  IonToast,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  heart,
  heartOutline,
  peopleOutline,
  timeOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';

const router = useRouter();
const authStore = useAuthStore();
const likedBranches = ref<any[]>([]);
const isLoading = ref(true);
const toastMessage = ref('');
const categoryMap = ref(new Map<string, string>());

const loadData = async () => {
  try {
    isLoading.value = true;

    if (!authStore.isAuthenticated || !authStore.currentUser?.id) {
      router.push('/login');
      return;
    }

    // Load branch categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('branch_categories')
      .select('*')
      .order('title', { ascending: true });

    if (categoriesError) throw categoriesError;
    
    // Create category map for quick lookups
    categoryMap.value = new Map(
      categoriesData.map(c => [c.id, c.title])
    );

    // Load user's liked branches
    const { data: likedData, error: likedError } = await supabase
      .from('user_liked_branches')
      .select('branch_id')
      .eq('user_id', authStore.currentUser.id);

    if (likedError) throw likedError;

    if (likedData && likedData.length > 0) {
      // Get the branch details for liked branches
      const branchIds = likedData.map(item => item.branch_id);
      
      const { data: branchesData, error: branchesError } = await supabase
        .from('branches')
        .select('*')
        .in('id', branchIds);

      if (branchesError) throw branchesError;
      likedBranches.value = branchesData;
    } else {
      likedBranches.value = [];
    }
  } catch (error) {
    console.error('Error loading liked branches:', error);
    toastMessage.value = '載入資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Use Ionic lifecycle hook
onIonViewDidEnter(() => {
  loadData();
});

const toggleFavorite = async (branch: any) => {
  try {
    // Remove from favorites in database
    const { error } = await supabase
      .from('user_liked_branches')
      .delete()
      .eq('user_id', authStore.currentUser?.id)
      .eq('branch_id', branch.id);

    if (error) throw error;
    
    // Remove from local list
    likedBranches.value = likedBranches.value.filter(b => b.id !== branch.id);
    toastMessage.value = '已從收藏移除';
  } catch (error) {
    console.error('Error removing from favorites:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};

const getCategoryTitle = (categoryId: string) => {
  return categoryMap.value.get(categoryId) || '其他';
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};
</script>

<style scoped>
.loading-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.empty-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--ion-color-medium);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: var(--ion-color-medium);
}

.browse-button {
  margin-top: 1.5rem;
}

.branches-container {
  width: 100%;
}

.branches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.branch-card {
  margin: 0;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.branch-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.banner-container {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.branch-banner {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.branch-card:hover .branch-banner {
  transform: scale(1.05);
}

.branch-actions {
  position: absolute;
  top: 8px;
  right: 8px;
}

.action-button {
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
  --background: rgba(255, 255, 255, 0.8);
  --color: var(--ion-color-danger);
  --border-radius: 50%;
}

.branch-info {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.branch-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 1rem;
  object-fit: cover;
  border: 2px solid var(--ion-color-light);
}

.branch-details {
  flex: 1;
}

.branch-meta {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.category-tag,
.district-tag {
  font-size: 0.7rem;
  padding: 2px 8px;
  border-radius: 12px;
  background: var(--ion-color-light);
  color: var(--ion-color-medium);
}

.branch-stats {
  display: flex;
  justify-content: space-between;
  color: var(--ion-color-medium);
  font-size: 0.85rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

@media (max-width: 576px) {
  .branches-grid {
    grid-template-columns: 1fr;
  }
}
</style>
