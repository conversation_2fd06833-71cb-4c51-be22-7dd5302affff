<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/home" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>我的收藏</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <LoadingSpinner v-if="isLoading" />

      <div v-else class="page-container">
        <!-- Products Grid -->
        <div class="products-grid ion-padding" v-if="likedProducts.length > 0">
          <ion-card
            v-for="product in likedProducts"
            :key="product.id"
            class="product-card"
            button
            :router-link="`/shops/${product.shop_id}/products/${product.id}`"
          >
            <div class="product-image-container">
              <img :src="product.cover_image" :alt="product.title" />
              <div class="product-actions">
                <ion-button fill="clear" @click.prevent.stop="toggleFavorite(product)" class="action-button">
                  <ion-icon :icon="heart" />
                </ion-button>
              </div>
              <ion-badge
                :color="product.is_in_stock ? 'success' : 'medium'"
                class="stock-badge"
              >
                {{ product.is_in_stock ? '有貨' : '缺貨' }}
              </ion-badge>
              <ion-badge
                v-if="product.category_id"
                color="primary"
                class="category-badge"
              >
                {{ getCategoryName(product.category_id) }}
              </ion-badge>
            </div>
            <ion-card-header>
              <div class="shop-info">
                <img :src="product.shops?.logo || 'https://via.placeholder.com/40'" :alt="getShopName(product.shop_id)" class="shop-logo">
                <span class="shop-name">{{ getShopName(product.shop_id) }}</span>
              </div>
              <ion-card-title>{{ product.title }}</ion-card-title>
              <div class="price-row">
                <ion-card-subtitle>HK$ {{ product.price }}</ion-card-subtitle>
                <div class="rating" v-if="product.rating">
                  <ion-icon :icon="star" color="warning"></ion-icon>
                  <span>{{ product.rating }}</span>
                </div>
              </div>
            </ion-card-header>
          </ion-card>
        </div>

        <!-- Empty State -->
        <div v-if="!isLoading && likedProducts.length === 0" class="empty-state ion-padding">
          <ion-icon :icon="heartOutline" color="medium"></ion-icon>
          <p>您還沒有收藏任何產品</p>
          <ion-button router-link="/products" expand="block" class="browse-button">
            瀏覽產品
          </ion-button>
        </div>

        <!-- Toast Messages -->
        <ion-toast
          :is-open="!!toastMessage"
          :message="toastMessage"
          :duration="3000"
          @didDismiss="toastMessage = ''"
        ></ion-toast>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonButton,
  IonIcon,
  IonToast,
  IonBadge,
  IonButtons,
  IonBackButton,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  heartOutline,
  heart,
  star,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import LoadingSpinner from '@/components/LoadingSpinner.vue';

const router = useRouter();
const authStore = useAuthStore();
const likedProducts = ref<any[]>([]);
const shops = ref<Map<string, string>>(new Map());
const categories = ref<any[]>([]);
const categoryMap = ref(new Map<string, string>());
const isLoading = ref(true);
const toastMessage = ref('');

// Check if user is authenticated
onMounted(() => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
  }
});

const loadData = async () => {
  try {
    isLoading.value = true;

    if (!authStore.isAuthenticated || !authStore.currentUser?.id) {
      router.push('/login');
      return;
    }

    // Load categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('product_categories')
      .select('*')
      .order('id', { ascending: true });

    if (categoriesError) throw categoriesError;
    categories.value = categoriesData;

    // Create category map for quick lookups
    categoryMap.value = new Map(
      categoriesData.map(c => [c.id.toString(), c.title])
    );

    // Load user's liked products
    const { data: likedData, error: likedError } = await supabase
      .from('user_liked_products')
      .select('product_id')
      .eq('user_id', authStore.currentUser.id);

    if (likedError) throw likedError;

    if (likedData && likedData.length > 0) {
      // Get the product details for liked products
      const productIds = likedData.map(item => item.product_id);

      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select(`
          *,
          shops (
            id,
            name,
            logo
          )
        `)
        .in('id', productIds)
        .eq('status', 'active');

      if (productsError) throw productsError;
      likedProducts.value = productsData;

      // Create shops map
      shops.value = new Map(
        productsData
          .filter(p => p.shops)
          .map(p => [p.shop_id, p.shops.name])
      );
    } else {
      likedProducts.value = [];
    }
  } catch (error) {
    console.error('Error loading liked products:', error);
    toastMessage.value = '載入資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Use Ionic lifecycle hook
onIonViewDidEnter(() => {
  loadData();
});

const getShopName = (shopId: string) => {
  return shops.value.get(shopId) || '未知商店';
};

const getCategoryName = (categoryId: string) => {
  return categoryMap.value.get(categoryId) || '未分類';
};

const toggleFavorite = async (product: any) => {
  try {
    // Remove from favorites in database
    const { error } = await supabase
      .from('user_liked_products')
      .delete()
      .eq('user_id', authStore.currentUser?.id)
      .eq('product_id', product.id);

    if (error) throw error;

    // Remove from local list
    likedProducts.value = likedProducts.value.filter(p => p.id !== product.id);
    toastMessage.value = '已從收藏移除';
  } catch (error) {
    console.error('Error removing from favorites:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};
</script>

<style scoped>
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 1rem;
  padding: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.product-card {
  margin: 0;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.product-image-container {
  position: relative;
  padding-top: 100%;
  overflow: hidden;
}

.product-image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image-container img {
  transform: scale(1.05);
}

.product-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 0.25rem;
  opacity: 1;
  transform: translateX(0);
}

.action-button {
  --background: transparent;
  --border-radius: 4px;
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  --padding-top: 0.5rem;
  --padding-bottom: 0.5rem;
  margin: 0;
  width: 36px;
  height: 36px;
}

.action-button ion-icon {
  font-size: 1.25rem;
  color: var(--ion-color-primary);
}

.stock-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  border-radius: 20px;
  padding: 4px 8px;
}

.category-badge {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  border-radius: 20px;
  padding: 4px 8px;
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

ion-card-header {
  padding: 0.75rem;
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.shop-logo {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
}

.shop-name {
  font-size: 0.75rem;
  color: var(--ion-color-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

ion-card-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

ion-card-subtitle {
  font-size: 1rem;
  color: var(--ion-color-primary);
  font-weight: 600;
  margin: 0;
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--ion-color-medium);
  font-size: 0.8rem;
}

.rating ion-icon {
  font-size: 0.9rem;
}

.empty-state {
  height: 50vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.empty-state ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0 0 1.5rem;
  color: var(--ion-color-medium);
}

.browse-button {
  max-width: 200px;
}

/* Desktop optimizations */
@media (min-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .product-actions {
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease;
  }

  .product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (min-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 2rem;
    padding: 2rem;
  }
}
</style>
