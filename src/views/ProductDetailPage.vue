<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button :default-href="`/shops/${shopId}`" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>{{ product?.title || '產品詳情' }}</ion-title>
        <ion-buttons slot="end">
          <!-- TODO: linked to user_faviorite_products
          <ion-button @click="toggleFavorite">
            <ion-icon :icon="isFavorite ? heart : heartOutline"></ion-icon>
          </ion-button>
          <ion-button>
            <ion-icon :icon="shareOutline"></ion-icon>
          </ion-button>-->
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <LoadingSpinner v-if="isLoading" />

      <div v-else-if="product" class="page-container">
        <!-- Mobile Layout -->
        <div class="mobile-layout">
          <!-- Image Gallery -->
          <div class="image-gallery">
            <div class="main-image">
              <img :src="selectedImage" :alt="product.title" />
              <div class="image-counter" v-if="allProductImages.length > 1">
                {{ currentImageIndex + 1 }}/{{ allProductImages.length }}
              </div>
            </div>
            <div class="thumbnails" v-if="allProductImages.length > 1">
              <div
                v-for="(image, index) in allProductImages"
                :key="index"
                class="thumbnail"
                :class="{ active: image.url === selectedImage }"
                @click="selectImage(image, index)"
              >
                <img :src="image.url" :alt="`${product.title} - ${image.caption || `Image ${index + 1}`}`" />
                <div class="caption-overlay" v-if="image.caption">{{ image.caption }}</div>
              </div>
            </div>
          </div>

          <!-- Quick Info -->
          <div class="quick-info">
            <div class="price-section">
              <div class="price">HK$ {{ product.price }}</div>
              <div class="stock-status" :class="{ 'out-of-stock': !product.is_in_stock }">
                {{ product.is_in_stock ? '有貨' : '缺貨' }}
              </div>
            </div>
            <h1>{{ product.title }}</h1>
            <div class="shop-info" @click="navigateToShop" role="button">
              <img :src="product.shops?.logo || 'https://via.placeholder.com/40'" alt="Shop logo" class="shop-logo">
              <span>{{ shopName }}</span>
              <ion-icon :icon="chevronForward" class="shop-link-icon"></ion-icon>
            </div>
          </div>

          <!-- Product Details -->
          <div class="product-details">
            <div class="section-title">產品詳情</div>
            <div class="product-description" v-html="product.description"></div>
          </div>

          <!-- Related Products -->
          <div class="related-products" v-if="relatedProducts.length > 0">
            <div class="section-title">相關產品</div>
            <div class="related-products-scroll">
              <ion-card 
                v-for="relatedProduct in relatedProducts" 
                :key="relatedProduct.id" 
                class="related-product-card" 
                button 
                @click="navigateToProduct(relatedProduct)"
              >
                <img :src="relatedProduct.cover_image" :alt="relatedProduct.title" />
                <ion-card-header>
                  <ion-card-title>{{ relatedProduct.title }}</ion-card-title>
                  <ion-card-subtitle>HK$ {{ relatedProduct.price }}</ion-card-subtitle>
                </ion-card-header>
              </ion-card>
            </div>
          </div>
        </div>

        <!-- Desktop Layout -->
        <div class="desktop-layout">
          <div class="desktop-gallery">
            <div class="main-image">
              <img :src="selectedImage" :alt="product.title" />
            </div>
            <div class="thumbnails-vertical" v-if="allProductImages.length > 1">
              <div
                v-for="(image, index) in allProductImages"
                :key="index"
                class="thumbnail"
                :class="{ active: image.url === selectedImage }"
                @click="selectImage(image, index)"
              >
                <img :src="image.url" :alt="`${product.title} - ${image.caption || `Image ${index + 1}`}`" />
                <div class="caption-overlay" v-if="image.caption">{{ image.caption }}</div>
              </div>
            </div>
          </div>
          
          <div class="desktop-info">
            <div class="product-header">
              <h1>{{ product.title }}</h1>
              <div class="shop-info" @click="navigateToShop" role="button">
                <img :src="product.shops?.logo || 'https://via.placeholder.com/40'" alt="Shop logo" class="shop-logo">
                <span>{{ shopName }}</span>
                <ion-icon :icon="chevronForward" class="shop-link-icon"></ion-icon>
              </div>
            </div>

            <div class="price-section">
              <div class="price">HK$ {{ product.price }}</div>
              <div class="stock-status" :class="{ 'out-of-stock': !product.is_in_stock }">
                {{ product.is_in_stock ? '有貨' : '缺貨' }}
              </div>
            </div>

            <div class="product-description" v-html="product.description"></div>

            <!--
            <div class="desktop-actions">
              <div class="quantity-selector">
                <ion-button
                  fill="clear"
                  :disabled="quantity <= 1"
                  @click="quantity = Math.max(1, quantity - 1)"
                >
                  <ion-icon :icon="removeOutline"></ion-icon>
                </ion-button>
                <ion-input
                  type="number"
                  v-model="quantity"
                  min="1"
                  class="quantity-input"
                ></ion-input>
                <ion-button
                  fill="clear"
                  @click="quantity++"
                >
                  <ion-icon :icon="addOutline"></ion-icon>
                </ion-button>
              </div>
              
              <ion-button
                expand="block"
                @click="addToCart"
                :disabled="!product.is_in_stock"
              >
                <ion-icon :icon="cartOutline" slot="start"></ion-icon>
                {{ product.is_in_stock ? '加入購物車' : '缺貨' }}
              </ion-button>
            </div>
            -->
          </div>
        </div>

        <!-- Fixed Bottom Bar 
        <div class="bottom-bar">
          <div class="quantity-selector">
            <ion-button
              fill="clear"
              :disabled="quantity <= 1"
              @click="quantity = Math.max(1, quantity - 1)"
            >
              <ion-icon :icon="removeOutline"></ion-icon>
            </ion-button>
            <ion-input
              type="number"
              v-model="quantity"
              min="1"
              class="quantity-input"
            ></ion-input>
            <ion-button
              fill="clear"
              @click="quantity++"
            >
              <ion-icon :icon="addOutline"></ion-icon>
            </ion-button>
          </div>
          <ion-button
            expand="block"
            @click="addToCart"
            :disabled="!product.is_in_stock"
          >
            <ion-icon :icon="cartOutline" slot="start"></ion-icon>
            {{ product.is_in_stock ? '加入購物車' : '缺貨' }}
          </ion-button>
        </div>
        -->
      </div>

      <div v-else class="page-container">
        <div class="error-container">
          <ion-icon :icon="alertCircleOutline" color="danger"></ion-icon>
          <p>找不到產品</p>
        </div>
      </div>

      <!-- Toast Messages -->
      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonToast,
  IonInput,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  onIonViewWillEnter,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  heartOutline,
  heart,
  cartOutline,
  alertCircleOutline,
  pencilOutline,
  trashOutline,
  menuOutline,
  eyeOutline,
  closeCircleOutline,
  addOutline,
  removeOutline,
  shareOutline,
  chevronForward,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useCartStore } from '@/stores/cart';
import LoadingSpinner from '@/components/LoadingSpinner.vue';

interface ProductImage {
  url: string;
  caption?: string;
}

const route = useRoute();
const router = useRouter();
const cartStore = useCartStore();

const shopId = route.params.shopId as string;
const productId = route.params.productId as string;

const product = ref<any>(null);
const shopName = ref('');
const productPhotos = ref<any[]>([]);
const relatedProducts = ref<any[]>([]);
const isLoading = ref(true);
const toastMessage = ref('');
const quantity = ref(1);
const isFavorite = ref(false);
const selectedImage = ref<string>('');
const currentImageIndex = ref(0);

const allProductImages = computed(() => {
  if (!product.value) return [];
  const images: ProductImage[] = [{ url: product.value.cover_image }];
  productPhotos.value.forEach(photo => {
    images.push({
      url: photo.photo_url,
      caption: photo.caption
    });
  });
  return images;
});

onIonViewDidEnter(() => {
  loadProductData();
});

const loadProductData = async () => {
  try {
    isLoading.value = true;

    // Load product details
    const { data: productData, error: productError } = await supabase
      .from('products')
      .select(`
        *,
        shops (
          name,
          logo
        )
      `)
      .eq('id', productId)
      .eq('status', 'active')
      .single();

    if (productError) throw productError;
    if (!productData) {
      router.push('/shop');
      return;
    }

    product.value = productData;
    shopName.value = productData.shops.name;
    selectedImage.value = productData.cover_image;

    // Load product photos with captions
    const { data: photos, error: photosError } = await supabase
      .from('product_photos')
      .select('*')
      .eq('product_id', productId)
      .order('order', { ascending: true });

    if (photosError) throw photosError;
    productPhotos.value = photos || [];

    // Load related products
    const { data: related, error: relatedError } = await supabase
      .from('products')
      .select('*')
      .eq('shop_id', shopId)
      .eq('status', 'active')
      .neq('id', productId)
      .limit(10);

    if (relatedError) throw relatedError;
    relatedProducts.value = related || [];

  } catch (error) {
    console.error('Error loading product:', error);
    toastMessage.value = '載入產品資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

const selectImage = (image: ProductImage, index: number) => {
  selectedImage.value = image.url;
  currentImageIndex.value = index;
};

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  toastMessage.value = isFavorite.value ? '已加入收藏' : '已移除收藏';
};

const addToCart = () => {
  if (product.value && quantity.value > 0) {
    cartStore.addItem(product.value, quantity.value);
    toastMessage.value = '已加入購物車';
  }
};

const navigateToProduct = (product: any) => {
  router.push(`/shops/${shopId}/products/${product.id}`);
};

const navigateToShop = () => {
  router.push(`/shops/${shopId}`);
};
</script>

<style scoped>
.shop-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ion-color-medium);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.shop-info:hover {
  background-color: var(--ion-color-light);
}

.shop-link-icon {
  font-size: 16px;
  color: var(--ion-color-medium);
}

/* Mobile-first styles */
.mobile-layout {
  display: block;
}

.desktop-layout {
  display: none;
}

.image-gallery {
  position: relative;
  background: var(--ion-color-light);
}

.main-image {
  position: relative;
  width: 100%;
  height: 300px;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-counter {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.thumbnails {
  display: flex;
  gap: 8px;
  padding: 8px;
  overflow-x: auto;
  background: white;
}

.thumbnail {
  position: relative;
  flex: 0 0 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  border: 2px solid transparent;
  cursor: pointer;
}

.thumbnail.active {
  border-color: var(--ion-color-primary);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.caption-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 4px;
  font-size: 0.7rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.quick-info {
  padding: 16px;
  background: white;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.price {
  font-size: 24px;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.stock-status {
  font-size: 14px;
  color: var(--ion-color-success);
}

.stock-status.out-of-stock {
  color: var(--ion-color-medium);
}

.quick-info h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px;
  line-height: 1.4;
}

.shop-logo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.product-details {
  margin-top: 8px;
  padding: 16px;
  background: white;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--ion-color-light);
}

.product-description {
  font-size: 14px;
  line-height: 1.6;
  color: var(--ion-color-dark);
}

.related-products {
  margin-top: 8px;
  padding: 16px;
  background: white;
}

.related-products-scroll {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 4px 0;
}

.related-product-card {
  flex: 0 0 140px;
  margin: 0;
  border-radius: 8px;
}

.related-product-card img {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
}

.related-product-card ion-card-header {
  padding: 8px;
}

.related-product-card ion-card-title {
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-product-card ion-card-subtitle {
  font-size: 14px;
  color: var(--ion-color-primary);
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: white;
  border-top: 1px solid var(--ion-color-light);
  z-index: 100;
}

.quantity-selector {
  display: flex;
  align-items: center;
  background: var(--ion-color-light);
  border-radius: 8px;
  padding: 0 4px;
}

.quantity-input {
  width: 40px;
  text-align: center;
  --padding-start: 0;
  --padding-end: 0;
}

ion-button[expand="block"] {
  margin: 0;
  flex: 1;
}

/* Add padding to content to account for bottom bar */
ion-content::part(scroll) {
  padding-bottom: 80px;
}

/* Desktop styles */
@media (min-width: 768px) {
  .mobile-layout {
    display: none;
  }

  .desktop-layout {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    gap: 48px;
  }

  .desktop-gallery {
    flex: 1;
    display: flex;
    gap: 16px;
  }

  .desktop-gallery .main-image {
    flex: 1;
    height: 500px;
    background: var(--ion-color-light);
    border-radius: 16px;
    overflow: hidden;
  }

  .thumbnails-vertical {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 80px;
  }

  .thumbnails-vertical .thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid transparent;
    cursor: pointer;
  }

  .thumbnails-vertical .thumbnail.active {
    border-color: var(--ion-color-primary);
  }

  .desktop-info {
    flex: 1;
    max-width: 500px;
    padding: 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .product-header {
    margin-bottom: 24px;
  }

  .product-header h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 16px;
    line-height: 1.4;
  }

  .desktop-info .price-section {
    margin-bottom: 24px;
  }

  .desktop-info .price {
    font-size: 32px;
  }

  .desktop-info .product-description {
    margin-bottom: 32px;
    font-size: 16px;
  }

  .desktop-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .desktop-actions .quantity-selector {
    align-self: flex-start;
  }

  .desktop-actions ion-button[expand="block"] {
    height: 48px;
    font-size: 16px;
  }

  /* Hide bottom bar on desktop */
  .bottom-bar {
    display: none;
  }

  /* Remove bottom padding since bottom bar is hidden */
  ion-content::part(scroll) {
    padding-bottom: 0;
  }
}

/* Hide scrollbars */
.thumbnails::-webkit-scrollbar,
.related-products-scroll::-webkit-scrollbar {
  display: none;
}

.thumbnails,
.related-products-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.loading-container,
.error-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}
</style>