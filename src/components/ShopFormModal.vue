<template>
  <ion-modal :is-open="isOpen" @didDismiss="closeModal">
    <ion-header>
      <ion-toolbar>
        <ion-title>{{ shop ? '編輯商店' : '創建商店' }}</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="closeModal">取消</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <form @submit.prevent="handleSubmit">
        <ion-list>
          <ion-item>
            <ion-label position="stacked">商店名稱 *</ion-label>
            <ion-input
              v-model="formData.name"
              type="text"
              required
              placeholder="請輸入商店名稱"
            ></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">商店簡介</ion-label>
            <ion-textarea
              v-model="formData.description"
              placeholder="請輸入商店簡介"
              :rows="4"
            ></ion-textarea>
          </ion-item>

          <!-- Shop Category Selection -->
          <ion-item>
            <ion-label position="stacked">商店類別 *</ion-label>
            <ion-select
              v-model="formData.shop_category_id"
              interface="popover"
              placeholder="請選擇商店類別"
              required
            >
              <ion-select-option v-for="category in shopCategories" :key="category.id" :value="category.id">
                {{ category.title }}
              </ion-select-option>
            </ion-select>
          </ion-item>

          <!-- Logo Upload -->
          <ion-item>
            <ion-label position="stacked">商店標誌</ion-label>
            <div class="image-upload-container">
              <div class="preview-container" v-if="logoPreview || formData.logo">
                <img :src="logoPreview || formData.logo" alt="Logo preview" class="image-preview logo-preview" />
                <ion-button fill="clear" color="danger" @click="removeLogo">
                  <ion-icon :icon="trashOutline"></ion-icon>
                </ion-button>
              </div>
              <ion-button v-else expand="block" @click="takeLogo" fill="outline">
                <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
                選擇標誌
              </ion-button>
            </div>
          </ion-item>

          <!-- Banner Upload -->
          <ion-item>
            <ion-label position="stacked">商店橫幅</ion-label>
            <div class="image-upload-container">
              <div class="preview-container" v-if="bannerPreview || formData.banner">
                <img :src="bannerPreview || formData.banner" alt="Banner preview" class="image-preview banner-preview" />
                <ion-button fill="clear" color="danger" @click="removeBanner">
                  <ion-icon :icon="trashOutline"></ion-icon>
                </ion-button>
              </div>
              <ion-button v-else expand="block" @click="takeBanner" fill="outline">
                <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
                選擇橫幅
              </ion-button>
            </div>
          </ion-item>
        </ion-list>

        <div class="ion-padding">
          <ion-button
            type="submit"
            expand="block"
            :disabled="isSubmitting"
          >
            <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
            <span v-else>{{ shop ? '更新' : '創建' }}</span>
          </ion-button>
        </div>
      </form>
    </ion-content>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonButton,
  IonButtons,
  IonIcon,
  IonSpinner,
  IonSelect,
  IonSelectOption,
} from '@ionic/vue';
import {
  cameraOutline,
  trashOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { uploadImages } from '@/lib/cloudflare';
import { usePhotoGallery, Photo } from '@/composables/usePhotoGallery';
import { utils } from '@/composables/utils';

const { presentToast } = utils();

const props = defineProps<{
  isOpen: boolean;
  shop?: {
    id: string;
    name: string;
    description?: string;
    logo?: string;
    banner?: string;
  } | null;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'created', shop: any): void;
  (e: 'updated', shop: any): void;
}>();

const authStore = useAuthStore();
const isSubmitting = ref(false);

// Initialize photo gallery
const { takePhoto } = usePhotoGallery();

// Image handling variables
const logoPreview = ref<string | null>(null);
const bannerPreview = ref<string | null>(null);
const logoPhoto = ref<Photo | null>(null);
const bannerPhoto = ref<Photo | null>(null);

const shopCategories = ref<Array<{ id: number, title: string }>>([]);

const formData = ref({
  name: '',
  description: '',
  logo: '',
  banner: '',
  shop_category_id: null as number | null,
});

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    logo: '',
    banner: '',
    shop_category_id: null,
  };
  logoPreview.value = null;
  bannerPreview.value = null;
  logoPhoto.value = null;
  bannerPhoto.value = null;
};

const initializeForm = (shop: any) => {
  if (shop) {
    formData.value = {
      name: shop.name,
      description: shop.description || '',
      logo: shop.logo || '',
      banner: shop.banner || '',
      shop_category_id: shop.shop_category_id || null,
    };
  } else {
    resetForm();
  }
};

watch(() => props.shop, (newShop) => {
  initializeForm(newShop);
}, { immediate: true });

const loadCategories = async () => {
  try {
    const { data, error } = await supabase
      .from('shop_categories')
      .select('*')
      .order('title', { ascending: true });

    if (error) throw error;
    shopCategories.value = data || [];
  } catch (error) {
    console.error('Error loading shop categories:', error);
    presentToast('載入商店類別失敗', 2000);
  }
};

onMounted(() => {
  loadCategories();
});

const closeModal = () => {
  emit('close');
  //resetForm();
};

const takeLogo = async () => {
  try {
    const photo = await takePhoto();
    if (photo) {
      logoPhoto.value = photo;
      logoPreview.value = photo.base64Data || null;
    }
  } catch (error) {
    console.error('Error taking logo photo:', error);
    presentToast('無法獲取相片，請稍後再試', 2000);
  }
};

const takeBanner = async () => {
  try {
    const photo = await takePhoto();
    if (photo) {
      bannerPhoto.value = photo;
      bannerPreview.value = photo.base64Data || null;
    }
  } catch (error) {
    console.error('Error taking banner photo:', error);
    presentToast('無法獲取相片，請稍後再試', 2000);
  }
};

const removeLogo = () => {
  logoPhoto.value = null;
  logoPreview.value = null;
  formData.value.logo = '';
};

const removeBanner = () => {
  bannerPhoto.value = null;
  bannerPreview.value = null;
  formData.value.banner = '';
};

const handleSubmit = async () => {
  if (!authStore.currentUser?.id) return;

  try {
    isSubmitting.value = true;

    // Upload logo and banner if provided
    let logoUrl = formData.value.logo;
    let bannerUrl = formData.value.banner;

    // Prepare images for upload
    const imageInputs = [];

    if (logoPhoto.value && logoPhoto.value.base64Data) {
      imageInputs.push({
        base64Data: logoPhoto.value.base64Data,
        filename: logoPhoto.value.filepath || `logo_${new Date().getTime()}.jpg`,
        mimeType: logoPhoto.value.mimeType || 'image/jpeg'
      });
    }

    if (bannerPhoto.value && bannerPhoto.value.base64Data) {
      imageInputs.push({
        base64Data: bannerPhoto.value.base64Data,
        filename: bannerPhoto.value.filepath || `banner_${new Date().getTime()}.jpg`,
        mimeType: bannerPhoto.value.mimeType || 'image/jpeg'
      });
    }

    // Upload images if any
    if (imageInputs.length > 0) {
      try {
        const uploadedUrls = await uploadImages(imageInputs);

        // Assign URLs to the appropriate variables
        if (logoPhoto.value && logoPhoto.value.base64Data) {
          logoUrl = uploadedUrls.shift() || '';
        }

        if (bannerPhoto.value && bannerPhoto.value.base64Data) {
          bannerUrl = uploadedUrls.shift() || '';
        }
      } catch (error) {
        console.error('Error uploading images:', error);
        presentToast('圖片上傳失敗，請稍後再試', 2000);
        throw error;
      }
    }

    if (!formData.value.name || !formData.value.shop_category_id) {
      presentToast('請填寫必填欄位', 2000);
      return;
    }

    if (props.shop) {
      // Update existing shop
      const { data: updatedShop, error } = await supabase
        .from('shops')
        .update({
          name: formData.value.name,
          description: formData.value.description,
          logo: logoUrl,
          banner: bannerUrl,
          shop_category_id: formData.value.shop_category_id,
        })
        .eq('id', props.shop.id)
        .select()
        .single();

      if (error) throw error;
      emit('updated', updatedShop);
    } else {
      // Create new shop
      const { data: newShop, error } = await supabase
        .from('shops')
        .insert({
          name: formData.value.name,
          description: formData.value.description,
          logo: logoUrl,
          banner: bannerUrl,
          shop_category_id: formData.value.shop_category_id,
          owner_id: authStore.currentUser.id,
        })
        .select()
        .single();

      if (error) throw error;
      emit('created', newShop);
    }

    closeModal();
  } catch (error: any) {
    console.error('Shop operation failed:', error);
    presentToast(error.message, 2000);
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.image-upload-container {
  width: 100%;
  padding: 1rem 0;
}

.preview-container {
  position: relative;
  display: inline-block;
}

.preview-container ion-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
}

.image-preview {
  border-radius: 8px;
  border: 1px solid var(--ion-color-medium);
}

.logo-preview {
  width: 120px;
  height: 120px;
  object-fit: cover;
}

.banner-preview {
  width: 100%;
  height: 200px;
  object-fit: cover;
}
</style>