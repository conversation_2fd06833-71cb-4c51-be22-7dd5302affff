<template>
  <ion-modal :is-open="isOpen" @didDismiss="closeModal">
    <ion-header>
      <ion-toolbar>
        <ion-title>更改密碼</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="closeModal">取消</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <form @submit.prevent="handleSubmit">
        <ion-list class="form-list">
          <ion-item-group>
            <ion-item-divider>
              <ion-label>密碼設定</ion-label>
            </ion-item-divider>

            <ion-item class="custom-item">
              <ion-icon :icon="lockClosedOutline" slot="start" class="form-icon"></ion-icon>
              <ion-input
                label="目前密碼*"
                label-placement="floating"
                type="password"
                v-model="formData.currentPassword"
                required
                :class="{ 'ion-invalid': errors.currentPassword }"
                helper-text="請輸入您目前的密碼以確認身份"
              ></ion-input>
            </ion-item>
            <div v-if="errors.currentPassword" class="error-message">{{ errors.currentPassword }}</div>

            <ion-item class="custom-item">
              <ion-icon :icon="lockClosedOutline" slot="start" class="form-icon"></ion-icon>
              <ion-input
                label="新密碼*"
                label-placement="floating"
                type="password"
                v-model="formData.newPassword"
                required
                :class="{ 'ion-invalid': errors.newPassword }"
              ></ion-input>
            </ion-item>
            <div v-if="errors.newPassword" class="error-message">{{ errors.newPassword }}</div>

            <ion-item class="custom-item">
              <ion-icon :icon="lockClosedOutline" slot="start" class="form-icon"></ion-icon>
              <ion-input
                label="確認新密碼*"
                label-placement="floating"
                type="password"
                v-model="formData.confirmPassword"
                required
                :class="{ 'ion-invalid': errors.confirmPassword }"
              ></ion-input>
            </ion-item>
            <div v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</div>
          </ion-item-group>

          <div class="password-requirements">
            <ion-text color="medium">
              <p>密碼要求：</p>
              <ul>
                <li>至少6個字符</li>
                <li>建議包含大小寫字母、數字和特殊字符</li>
              </ul>
            </ion-text>
          </div>
        </ion-list>

        <div class="ion-padding">
          <ion-button
            expand="block"
            type="submit"
            :disabled="isSubmitting"
          >
            <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
            <span v-else>更改密碼</span>
          </ion-button>
        </div>
      </form>
    </ion-content>

    <ion-toast
      :is-open="!!toastMessage"
      :message="toastMessage"
      :duration="3000"
      @didDismiss="toastMessage = ''"
    ></ion-toast>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonList,
  IonItemGroup,
  IonItemDivider,
  IonItem,
  IonLabel,
  IonInput,
  IonIcon,
  IonSpinner,
  IonToast,
  IonText,
} from '@ionic/vue';
import { lockClosedOutline } from 'ionicons/icons';
import { supabase } from '@/lib/supabase';

const props = defineProps<{
  isOpen: boolean;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'updated'): void;
}>();

const isSubmitting = ref(false);
const toastMessage = ref('');
const errors = ref<Record<string, string>>({});

// Form data
const formData = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// Validation
const validateForm = () => {
  errors.value = {};

  if (!formData.value.currentPassword.trim()) {
    errors.value.currentPassword = '請輸入目前密碼';
  }

  if (!formData.value.newPassword.trim()) {
    errors.value.newPassword = '請輸入新密碼';
  } else if (formData.value.newPassword.length < 6) {
    errors.value.newPassword = '密碼至少需要6個字符';
  }

  if (!formData.value.confirmPassword.trim()) {
    errors.value.confirmPassword = '請確認新密碼';
  } else if (formData.value.newPassword !== formData.value.confirmPassword) {
    errors.value.confirmPassword = '新密碼與確認密碼不匹配';
  }

  return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm() || isSubmitting.value) return;

  try {
    isSubmitting.value = true;

    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user?.email) {
      throw new Error('無法獲取用戶信息');
    }

    // Verify current password by attempting to sign in with it
    // We'll use a separate client instance to avoid affecting the current session
    const { createClient } = await import('@supabase/supabase-js');
    const tempClient = createClient(
      import.meta.env.VITE_SUPABASE_URL,
      import.meta.env.VITE_SUPABASE_ANON_KEY
    );

    const { error: verifyError } = await tempClient.auth.signInWithPassword({
      email: user.email,
      password: formData.value.currentPassword
    });

    if (verifyError) {
      errors.value.currentPassword = '目前密碼不正確';
      return;
    }

    // Sign out the temporary session immediately
    await tempClient.auth.signOut();

    // Now update the password using the main client
    const { error: updateError } = await supabase.auth.updateUser({
      password: formData.value.newPassword
    });

    if (updateError) {
      throw updateError;
    }

    toastMessage.value = '密碼已成功更改';
    emit('updated');

    // Reset form
    formData.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    };

    // Close modal after a short delay
    setTimeout(() => {
      closeModal();
    }, 1500);
  } catch (error) {
    console.error('Error changing password:', error);
    toastMessage.value = '更改密碼失敗，請稍後再試';
  } finally {
    isSubmitting.value = false;
  }
};

const closeModal = () => {
  emit('close');
  // Reset form and errors when closing
  formData.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  };
  errors.value = {};
};
</script>

<style scoped>
.form-list {
  background: transparent;
}

.custom-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 0.5rem;
}

.form-icon {
  color: var(--ion-color-medium);
  margin-right: 1rem;
  font-size: 1.2rem;
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 0.875rem;
  padding: 0.25rem 1rem;
  margin-bottom: 0.5rem;
}

ion-item-group {
  background: white;
  border-radius: 12px;
  margin-bottom: 1rem;
  overflow: hidden;
}

ion-item-divider {
  --background: var(--ion-color-light);
  --color: var(--ion-color-dark);
  font-weight: 600;
  font-size: 0.9rem;
}

.password-requirements {
  padding: 1rem;
  background: var(--ion-color-light);
  border-radius: 8px;
  margin: 1rem 0;
}

.password-requirements p {
  margin: 0 0 0.5rem;
  font-weight: 600;
}

.password-requirements ul {
  margin: 0;
  padding-left: 1.5rem;
}

.password-requirements li {
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}
</style>
