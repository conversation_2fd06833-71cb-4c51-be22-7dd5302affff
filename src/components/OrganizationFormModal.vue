<template>
  <ion-modal :is-open="isOpen" @didDismiss="close">
    <ion-header>
      <ion-toolbar>
        <ion-title>{{ organization ? '編輯組織' : '創建新組織' }}</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="close">取消</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <form @submit.prevent="handleSubmit">
        <!-- Basic Information -->
        <ion-item>
          <ion-label position="stacked">組織名稱 <ion-text color="danger">*</ion-text></ion-label>
          <ion-input
            v-model="formData.name"
            placeholder="請輸入組織名稱"
            :class="{ 'has-error': errors.name }"
            required
          ></ion-input>
          <ion-note v-if="errors.name" color="danger">{{ errors.name }}</ion-note>
        </ion-item>

        <ion-item>
          <ion-label position="stacked">簡短描述</ion-label>
          <ion-textarea
            v-model="formData.description"
            placeholder="簡短描述組織的主要目標或特點"
            :rows="2"
          ></ion-textarea>
        </ion-item>

        <!-- Logo Upload -->
        <ion-item>
          <ion-label position="stacked">組織標誌</ion-label>
          <div class="image-upload-container">
            <div class="preview-container" v-if="logoPreview || formData.logo">
              <img :src="logoPreview || formData.logo" alt="Logo preview" class="image-preview logo-preview" />
              <ion-button fill="clear" color="danger" @click="removeLogo">
                <ion-icon :icon="trashOutline"></ion-icon>
              </ion-button>
            </div>
            <ion-button v-else expand="block" @click="takeLogo" fill="outline">
              <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
              選擇標誌
            </ion-button>
          </div>
        </ion-item>

        <!-- Banner Upload -->
        <ion-item>
          <ion-label position="stacked">組織橫幅</ion-label>
          <div class="image-upload-container">
            <div class="preview-container" v-if="bannerPreview || formData.banner">
              <img :src="bannerPreview || formData.banner" alt="Banner preview" class="image-preview banner-preview" />
              <ion-button fill="clear" color="danger" @click="removeBanner">
                <ion-icon :icon="trashOutline"></ion-icon>
              </ion-button>
            </div>
            <ion-button v-else expand="block" @click="takeBanner" fill="outline">
              <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
              選擇橫幅
            </ion-button>
          </div>
        </ion-item>

        <!-- Introduction -->
        <ion-item>
          <ion-label position="stacked">組織介紹</ion-label>
          <ion-textarea
            v-model="formData.introduction"
            placeholder="詳細介紹組織的背景、歷史和目標"
            :rows="4"
          ></ion-textarea>
        </ion-item>

        <!-- Philosophy -->
        <ion-item>
          <ion-label position="stacked">核心理念</ion-label>
          <ion-textarea
            v-model="formData.philosophy"
            placeholder="描述組織的核心價值觀和理念"
            :rows="4"
          ></ion-textarea>
        </ion-item>

        <div class="ion-padding">
          <ion-button
            type="submit"
            expand="block"
            :disabled="isSubmitting"
          >
            <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
            <span v-else>{{ organization ? '更新組織' : '創建組織' }}</span>
          </ion-button>
        </div>
      </form>
    </ion-content>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonButton,
  IonContent,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonNote,
  IonText,
  IonSpinner,
  IonIcon,
  loadingController,
  toastController
} from '@ionic/vue';
import {
  cameraOutline,
  trashOutline,
} from 'ionicons/icons';
import { useOrganizationStore } from '@/stores/organization';
import { uploadImages } from '@/lib/cloudflare';
import { usePhotoGallery, Photo } from '@/composables/usePhotoGallery';
import { utils } from '@/composables/utils';

const { presentToast } = utils();

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  organization: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'created', 'updated']);

const organizationStore = useOrganizationStore();
const isSubmitting = ref(false);

// Initialize photo gallery
const { takePhoto } = usePhotoGallery();

// Image handling variables
const logoPreview = ref<string | null>(null);
const bannerPreview = ref<string | null>(null);
const logoPhoto = ref<Photo | null>(null);
const bannerPhoto = ref<Photo | null>(null);

// Form data
const formData = reactive({
  name: '',
  description: '',
  logo: '',
  banner: '',
  introduction: '',
  philosophy: ''
});

// Image handling functions
const takeLogo = async () => {
  try {
    const photo = await takePhoto();
    if (photo) {
      logoPhoto.value = photo;
      logoPreview.value = photo.base64Data || null;
    }
  } catch (error) {
    console.error('Error taking logo photo:', error);
    presentToast('無法獲取相片，請稍後再試', 2000);
  }
};

const takeBanner = async () => {
  try {
    const photo = await takePhoto();
    if (photo) {
      bannerPhoto.value = photo;
      bannerPreview.value = photo.base64Data || null;
    }
  } catch (error) {
    console.error('Error taking banner photo:', error);
    presentToast('無法獲取相片，請稍後再試', 2000);
  }
};

const removeLogo = () => {
  logoPhoto.value = null;
  logoPreview.value = null;
  formData.logo = '';
};

const removeBanner = () => {
  bannerPhoto.value = null;
  bannerPreview.value = null;
  formData.banner = '';
};

// Form errors
const errors = reactive({
  name: ''
});

// Initialize form with organization data if editing
watch(() => props.organization, (newOrg) => {
  if (newOrg) {
    formData.name = newOrg.name || '';
    formData.description = newOrg.description || '';
    formData.logo = newOrg.logo || '';
    formData.banner = newOrg.banner || '';
    formData.introduction = newOrg.introduction || '';
    formData.philosophy = newOrg.philosophy || '';

    // Reset file previews
    logoPreview.value = null;
    bannerPreview.value = null;
    logoPhoto.value = null;
    bannerPhoto.value = null;
  } else {
    // Reset form for new organization
    formData.name = '';
    formData.description = '';
    formData.logo = '';
    formData.banner = '';
    formData.introduction = '';
    formData.philosophy = '';

    // Reset file previews
    logoPreview.value = null;
    bannerPreview.value = null;
    logoPhoto.value = null;
    bannerPhoto.value = null;
  }
}, { immediate: true });

// Validate form
const validateForm = () => {
  let isValid = true;
  errors.name = '';

  if (!formData.name.trim()) {
    errors.name = '組織名稱不能為空';
    isValid = false;
  }

  return isValid;
};

// Handle form submission
const handleSubmit = async () => {
  if (!validateForm()) return;

  try {
    isSubmitting.value = true;
    const loading = await loadingController.create({
      message: props.organization ? '更新中...' : '創建中...'
    });
    await loading.present();

    // Upload logo and banner if provided
    let logoUrl = formData.logo;
    let bannerUrl = formData.banner;

    // Prepare images for upload
    const imageInputs = [];

    if (logoPhoto.value && logoPhoto.value.base64Data) {
      imageInputs.push({
        base64Data: logoPhoto.value.base64Data,
        filename: logoPhoto.value.filepath || `logo_${new Date().getTime()}.jpg`,
        mimeType: logoPhoto.value.mimeType || 'image/jpeg'
      });
    }

    if (bannerPhoto.value && bannerPhoto.value.base64Data) {
      imageInputs.push({
        base64Data: bannerPhoto.value.base64Data,
        filename: bannerPhoto.value.filepath || `banner_${new Date().getTime()}.jpg`,
        mimeType: bannerPhoto.value.mimeType || 'image/jpeg'
      });
    }

    // Upload images if any
    if (imageInputs.length > 0) {
      try {
        const uploadedUrls = await uploadImages(imageInputs);

        // Assign URLs to the appropriate variables
        if (logoPhoto.value && logoPhoto.value.base64Data) {
          logoUrl = uploadedUrls.shift() || '';
        }

        if (bannerPhoto.value && bannerPhoto.value.base64Data) {
          bannerUrl = uploadedUrls.shift() || '';
        }
      } catch (error) {
        console.error('Error uploading images:', error);
        presentToast('圖片上傳失敗，請稍後再試', 2000);
        throw error;
      }
    }

    // Prepare data with updated image URLs
    const organizationData = {
      ...formData,
      logo: logoUrl,
      banner: bannerUrl
    };

    if (props.organization) {
      // Update existing organization
      const updatedOrg = await organizationStore.updateOrganization(
        props.organization.id,
        organizationData
      );
      emit('updated', updatedOrg);
    } else {
      // Create new organization
      const newOrg = await organizationStore.createOrganization(organizationData);
      emit('created', newOrg);
    }

    // Show success toast
    const toast = await toastController.create({
      message: props.organization ? '組織已更新' : '組織已創建',
      duration: 2000,
      color: 'success'
    });
    await toast.present();

    // Close modal
    emit('close');
  } catch (error: any) {
    console.error('Error submitting form:', error);
    const toast = await toastController.create({
      message: `操作失敗: ${error.message || '未知錯誤'}`,
      duration: 3000,
      color: 'danger'
    });
    await toast.present();
  } finally {
    isSubmitting.value = false;
    loadingController.dismiss();
  }
};

// Close modal
const close = () => {
  emit('close');
};
</script>

<style scoped>
ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
}

.has-error {
  --border-color: var(--ion-color-danger);
}

.image-upload-container {
  width: 100%;
  padding: 1rem 0;
}

.preview-container {
  position: relative;
  display: inline-block;
}

.preview-container ion-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
}

.image-preview {
  border-radius: 8px;
  border: 1px solid var(--ion-color-medium);
}

.logo-preview {
  width: 120px;
  height: 120px;
  object-fit: cover;
}

.banner-preview {
  width: 100%;
  height: 200px;
  object-fit: cover;
}
</style>
