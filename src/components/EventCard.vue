<template>
  <ion-card :class="['event-card', { 'past-event': isPastEvent }]" @click="navigateToEvent">
    <div class="event-image-container">
      <img v-if="event.banner_photo" :src="event.banner_photo" :alt="event.title" class="event-image" />
      <div v-else class="event-image-placeholder">
        <ion-icon :icon="calendarOutline" color="medium" size="large"></ion-icon>
      </div>
      <div class="event-date-badge">
        <div class="event-date-day">{{ formatDay(event.start_datetime) }}</div>
        <div class="event-date-month">{{ formatMonth(event.start_datetime) }}</div>
      </div>
      <div v-if="isPastEvent" class="event-status-badge">已結束</div>
      <div v-if="authStore.isAuthenticated" class="event-actions">
        <ion-button fill="clear" @click.prevent.stop="toggleFavorite" class="action-button">
          <ion-icon :icon="isEventLiked ? heart : heartOutline" />
        </ion-button>
      </div>
    </div>
    <ion-card-header>
      <ion-card-title>{{ event.title }}</ion-card-title>
      <div v-if="event.creator_full_name" class="event-creator">
        <ion-icon :icon="personOutline" color="medium"></ion-icon>
        <span>{{ event.creator_full_name }}</span>
      </div>
    </ion-card-header>
    <ion-card-content>
      <div class="event-details">
        <div class="event-time">
          <ion-icon :icon="calendarOutline" color="medium"></ion-icon>
          <span>{{ formatDateTimeDisplay(event.start_datetime) }}</span>
        </div>
        <div class="event-time">
          <ion-icon :icon="timeOutline" color="medium"></ion-icon>
          <span>至 {{ formatDateTimeDisplay(event.end_datetime) }}</span>
        </div>
        <div class="event-location">
          <ion-icon :icon="locationOutline" color="medium"></ion-icon>
          <span>{{ event.address }}</span>
        </div>
      </div>
    </ion-card-content>
  </ion-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonIcon,
  IonButton,
} from '@ionic/vue';
import {
  calendarOutline,
  timeOutline,
  locationOutline,
  personOutline,
  heart,
  heartOutline,
} from 'ionicons/icons';
import { utils } from '@/composables/utils';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';

const { formatDay, formatMonth, presentToast } = utils();

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
});

const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();

// Check if the event is liked by the current user
const isEventLiked = computed(() => {
  return userStore.isEventLiked(props.event.id);
});

// Check if the event is in the past
const isPastEvent = computed(() => {
  return new Date(props.event.end_datetime) < new Date();
});

// Format datetime for display (assuming input is already in HKT)
const formatDateTimeDisplay = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

const navigateToEvent = () => {
  router.push(`/events/${props.event.id}`);
};

// Toggle favorite status for the event
const toggleFavorite = async (event: Event) => {
  event.preventDefault();
  event.stopPropagation();

  if (!authStore.isAuthenticated) {
    presentToast('請先登入以收藏活動');
    return;
  }

  const result = await userStore.toggleEventFavorite(props.event.id);

  // Update like count in UI immediately
  if (result.success) {
    if (isEventLiked.value) {
      // Just added to favorites
      props.event.like_count = (props.event.like_count || 0) + 1;
    } else {
      // Just removed from favorites
      if (props.event.like_count && props.event.like_count > 0) {
        props.event.like_count -= 1;
      }
    }
  }

  presentToast(result.message);
};
</script>

<style scoped>
.event-card {
  margin: 0;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.event-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.event-image-container {
  position: relative;
  height: 160px;
  background-color: #f5f5f5;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.event-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f5f5;
}

.event-date-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background-color: white;
  border-radius: 8px;
  padding: 8px 12px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.event-date-day {
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1;
}

.event-date-month {
  font-size: 0.8rem;
  text-transform: uppercase;
}

.event-status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: var(--ion-color-medium);
  color: white;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8rem;
}

.event-actions {
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
  gap: 8px;
}

.action-button {
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
  height: auto;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}

.action-button ion-icon {
  color: var(--ion-color-danger);
  font-size: 1.2rem;
}

.past-event {
  opacity: 0.8;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.event-time, .event-location, .event-creator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.event-creator {
  margin-top: 4px;
  font-size: 0.85rem;
}

ion-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 4px;
}
</style>
