<template>
  <ion-modal :is-open="isOpen" @didDismiss="closeModal">
    <ion-header>
      <ion-toolbar>
        <ion-title>編輯個人資料</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="closeModal">取消</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <form @submit.prevent="handleSubmit">
        <ion-list class="form-list">
          <!-- Personal Information Section -->
          <div class="form-section">
            <ion-item-group>
              <ion-item-divider>
                <ion-label>基本資料</ion-label>
              </ion-item-divider>

              <ion-item class="custom-item">
                <ion-icon :icon="personOutline" slot="start" class="form-icon"></ion-icon>
                <ion-input
                  label="姓名*"
                  label-placement="floating"
                  type="text"
                  v-model="formData.fullName"
                  required
                  :class="{ 'ion-invalid': errors.fullName }"
                ></ion-input>
              </ion-item>
              <div v-if="errors.fullName" class="error-message">{{ errors.fullName }}</div>

              <ion-item class="custom-item">
                <ion-icon :icon="mailOutline" slot="start" class="form-icon"></ion-icon>
                <ion-input
                  label="電郵地址*"
                  label-placement="floating"
                  type="email"
                  v-model="formData.email"
                  required
                  :class="{ 'ion-invalid': errors.email }"
                ></ion-input>
              </ion-item>
              <div v-if="errors.email" class="error-message">{{ errors.email }}</div>

              <ion-item class="custom-item">
                <ion-icon :icon="globeOutline" slot="start" class="form-icon"></ion-icon>
                <ion-select
                  label="國家/地區*"
                  label-placement="floating"
                  v-model="formData.countryCode"
                >
                  <ion-select-option value="+852">香港 (+852)</ion-select-option>
                  <ion-select-option value="+853">澳門 (+853)</ion-select-option>
                  <ion-select-option value="+86">中國內地 (+86)</ion-select-option>
                </ion-select>
              </ion-item>

              <ion-item class="custom-item">
                <ion-icon :icon="callOutline" slot="start" class="form-icon"></ion-icon>
                <ion-input
                  label="電話號碼*"
                  label-placement="floating"
                  type="tel"
                  v-model="formData.phoneNumber"
                  required
                  :class="{ 'ion-invalid': errors.phoneNumber }"
                ></ion-input>
              </ion-item>
              <div v-if="errors.phoneNumber" class="error-message">{{ errors.phoneNumber }}</div>
            </ion-item-group>
          </div>

          <!-- Business Information Section (for merchant/president) -->
          <div class="form-section" v-if="isMerchantOrPresident">
            <ion-item-group>
              <ion-item-divider>
                <ion-label>商家資料</ion-label>
              </ion-item-divider>

              <ion-item class="custom-item">
                <ion-select
                  label="行業"
                  label-placement="floating"
                  v-model="formData.industry"
                >
                  <ion-select-option value="零售業">零售業</ion-select-option>
                  <ion-select-option value="餐飲業">餐飲業</ion-select-option>
                  <ion-select-option value="服務業">服務業</ion-select-option>
                  <ion-select-option value="製造業">製造業</ion-select-option>
                  <ion-select-option value="建築業">建築業</ion-select-option>
                  <ion-select-option value="金融業">金融業</ion-select-option>
                  <ion-select-option value="科技業">科技業</ion-select-option>
                  <ion-select-option value="教育業">教育業</ion-select-option>
                  <ion-select-option value="醫療業">醫療業</ion-select-option>
                  <ion-select-option value="運輸業">運輸業</ion-select-option>
                  <ion-select-option value="其他">其他</ion-select-option>
                </ion-select>
                <ion-icon :icon="businessOutline" slot="start" class="form-icon"></ion-icon>
              </ion-item>

              <ion-item class="custom-item" v-if="formData.industry === '其他'">
                <ion-input
                  label="請輸入行業"
                  label-placement="floating"
                  v-model="formData.otherIndustry"
                ></ion-input>
                <ion-icon :icon="businessOutline" slot="start" class="form-icon"></ion-icon>
              </ion-item>

              <ion-item class="custom-item">
                <ion-input
                  label="公司名稱"
                  label-placement="floating"
                  type="text"
                  v-model="formData.companyName"
                ></ion-input>
                <ion-icon :icon="briefcaseOutline" slot="start" class="form-icon"></ion-icon>
              </ion-item>
            </ion-item-group>
          </div>
        </ion-list>

        <div class="ion-padding">
          <ion-button
            expand="block"
            type="submit"
            :disabled="isSubmitting"
          >
            <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
            <span v-else>保存更改</span>
          </ion-button>
        </div>
      </form>
    </ion-content>

    <ion-toast
      :is-open="!!toastMessage"
      :message="toastMessage"
      :duration="3000"
      @didDismiss="toastMessage = ''"
    ></ion-toast>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonList,
  IonItemGroup,
  IonItemDivider,
  IonItem,
  IonLabel,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonIcon,
  IonSpinner,
  IonToast,
} from '@ionic/vue';
import {
  personOutline,
  mailOutline,
  callOutline,
  globeOutline,
  businessOutline,
  briefcaseOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import type { User } from '@/services/schema';

const props = defineProps<{
  isOpen: boolean;
  currentUser: User | null;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'updated'): void;
}>();

const authStore = useAuthStore();
const isSubmitting = ref(false);
const toastMessage = ref('');
const errors = ref<Record<string, string>>({});

// Form data
const formData = ref({
  fullName: '',
  email: '',
  countryCode: '+852',
  phoneNumber: '',
  industry: '',
  otherIndustry: '',
  companyName: ''
});

const isMerchantOrPresident = computed(() => {
  return props.currentUser?.role === 'merchant' || props.currentUser?.role === 'president';
});

// Watch for currentUser changes to populate form
watch(() => props.currentUser, (user) => {
  if (user) {
    // Parse phone number to extract country code and number
    const phone = user.phone || '';
    let countryCode = '+852';
    let phoneNumber = phone;
    
    if (phone.startsWith('+852')) {
      countryCode = '+852';
      phoneNumber = phone.substring(4);
    } else if (phone.startsWith('+853')) {
      countryCode = '+853';
      phoneNumber = phone.substring(4);
    } else if (phone.startsWith('+86')) {
      countryCode = '+86';
      phoneNumber = phone.substring(3);
    }

    // Parse industry for "其他" case
    let industry = user.industry || '';
    let otherIndustry = '';
    if (industry.startsWith('其他 - ')) {
      otherIndustry = industry.substring(5);
      industry = '其他';
    }

    formData.value = {
      fullName: user.full_name || '',
      email: user.email || '',
      countryCode,
      phoneNumber,
      industry,
      otherIndustry,
      companyName: user.company_name || ''
    };
  }
}, { immediate: true });

// Validation
const validateForm = () => {
  errors.value = {};
  
  if (!formData.value.fullName.trim()) {
    errors.value.fullName = '請輸入姓名';
  }
  
  if (!formData.value.email.trim()) {
    errors.value.email = '請輸入電郵地址';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.value.email)) {
    errors.value.email = '請輸入有效的電郵地址';
  }
  
  if (!formData.value.phoneNumber.trim()) {
    errors.value.phoneNumber = '請輸入電話號碼';
  }
  
  return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm() || isSubmitting.value || !props.currentUser) return;

  try {
    isSubmitting.value = true;

    // Prepare the update data
    const phone = `${formData.value.countryCode}${formData.value.phoneNumber}`;
    const industry = formData.value.industry === '其他' 
      ? `${formData.value.industry} - ${formData.value.otherIndustry}` 
      : formData.value.industry || null;

    const updateData = {
      full_name: formData.value.fullName.trim(),
      email: formData.value.email.trim().toLowerCase(),
      phone,
      industry,
      company_name: formData.value.companyName.trim() || null,
      updated_at: new Date().toISOString()
    };

    // Update the users table
    const { error: dbError } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', props.currentUser.id);

    if (dbError) throw dbError;

    // Update auth user email if it changed
    if (formData.value.email.trim().toLowerCase() !== props.currentUser.email) {
      const { error: authError } = await supabase.auth.updateUser({
        email: formData.value.email.trim().toLowerCase()
      });

      if (authError) throw authError;
    }

    // Refresh user data
    await authStore.refreshUserData();

    toastMessage.value = '個人資料已更新';
    emit('updated');
    
    // Close modal after a short delay
    setTimeout(() => {
      closeModal();
    }, 1500);
  } catch (error) {
    console.error('Error updating profile:', error);
    toastMessage.value = '更新失敗，請稍後再試';
  } finally {
    isSubmitting.value = false;
  }
};

const closeModal = () => {
  emit('close');
  // Reset errors when closing
  errors.value = {};
};
</script>

<style scoped>
.form-section {
  margin-bottom: 1rem;
}

.custom-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 0.5rem;
}

.form-icon {
  color: var(--ion-color-medium);
  margin-right: 1rem;
  font-size: 1.2rem;
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 0.875rem;
  padding: 0.25rem 1rem;
  margin-bottom: 0.5rem;
}

.form-list {
  background: transparent;
}

ion-item-group {
  background: white;
  border-radius: 12px;
  margin-bottom: 1rem;
  overflow: hidden;
}

ion-item-divider {
  --background: var(--ion-color-light);
  --color: var(--ion-color-dark);
  font-weight: 600;
  font-size: 0.9rem;
}
</style>
