// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-app')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-clipboard')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-live-updates')
    implementation project(':capacitor-share')
    implementation project(':capacitor-status-bar')
    implementation project(':capawesome-capacitor-android-edge-to-edge-support')
    implementation "com.onesignal:OneSignal:5.1.29"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"
}
apply from: "../../node_modules/onesignal-cordova-plugin/build-extras-onesignal.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
