import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.syner.biz',
  //appName: '商聯思維',
  appName: 'Synerthink',
  webDir: 'dist',
  ios: {
    handleApplicationNotifications: false
  },
  plugins: {
    LiveUpdates: {
      appId: 'f9cd15cc',
      channel: 'Production',
      autoUpdateMethod: 'background', // https://ionic.io/docs/appflow/deploy/setup/capacitor-sdk#force-update
      maxVersions: 3
    },
    Camera: {
      promptBeforeEnabling: true,
      promptMessage: "請允許使用相機以掃描QR碼"
    }
  }
};

export default config;
